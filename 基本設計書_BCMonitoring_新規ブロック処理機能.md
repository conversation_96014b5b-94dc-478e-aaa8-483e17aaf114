# 基本設計書_BCMonitoring_新規ブロック処理機能

## 1. 概要

### 機能の目的と責務
- **目的**: Ethereumブロックチェーンから新しく生成されるブロックをリアルタイムで監視し、含まれるイベントログを検知・保存する
- **責務**: 
  - Web3j WebSocketを使用した新規ブロック通知の購読
  - 新規ブロック内のトランザクションとイベントログの解析
  - ABI定義に基づくイベントフィルタリング
  - DynamoDBへのイベントデータとブロック高の保存

### 他機能との関連性
- **前提機能**: 
  - ABI.jsonパース機能（イベント定義の取得）
  - ブロックのログをパースする機能（イベントログの解析）
- **後続機能**: 
  - リトライの仕組み（エラー時の再処理）
- **並行機能**: 
  - 過去ブロック処理機能（過去ブロック処理完了後に開始）

### 前提条件・制約事項
- **前提条件**:
  - Web3j WebSocket接続が確立されていること
  - ABI JSONがS3から取得済みであること
  - DynamoDBテーブルが作成済みであること
  - 過去ブロック処理が完了していること
- **制約事項**:
  - 1つのWebSocket接続のみサポート
  - ブロック遅延検知機能あり（設定可能）
  - メモリ使用量制限（BlockingQueueサイズ: Integer.MAX_VALUE）

## 2. 機能仕様

### 入力・出力仕様

#### 入力
| 項目 | 型 | 説明 | 取得元 |
|------|----|----|--------|
| 新規ブロック通知 | NewHeadsNotification | Web3jからの新規ブロック通知 | Ethereum WebSocket |
| ABI定義 | Map<String, ContractEvents> | コントラクトアドレス別のイベント定義 | AbiParser.contractEventStore |

#### 出力
| 項目 | 型 | 説明 | 保存先 |
|------|----|----|--------|
| イベントレコード | Event | 検知されたイベント情報 | DynamoDB Events Table |
| ブロック高レコード | BlockHeight | 処理済みブロック高 | DynamoDB BlockHeight Table |

### 処理条件・判定ロジック

#### ブロック処理条件
```java
// 1. ブロックにトランザクションが存在する
if (block.getTransactions() == null || block.getTransactions().isEmpty()) {
    // スキップ
}

// 2. ブロック遅延チェック
if (isDelayed(block, allowableDiff)) {
    // 警告ログ出力（処理は継続）
}

// 3. イベントログのABI定義存在チェック
if (abiParser.getABIEventByLog(ethLog) != null) {
    // イベント処理実行
}
```

#### エラー判定条件
```java
// 1. WebSocket切断検知
if (tx.blockHeight.blockNumber == -1) {
    throw new Exception("Websocket is disconnected");
}

// 2. 不正なブロック高
if (tx.blockHeight.blockNumber == 0) {
    throw new Exception("Block height Number is zero");
}

// 3. トランザクションハッシュ不正
if (e.transactionHash == null || e.transactionHash.isEmpty()) {
    return false; // 保存失敗
}
```

### 設定パラメータ

| パラメータ名 | 型 | デフォルト値 | 説明 |
|-------------|----|-----------|----|
| subscription.checkInterval | String | - | エラー時のsleep間隔（ミリ秒） |
| subscription.allowableBlockTimestampDiffSec | String | - | ブロック遅延許容時間（秒） |
| ethereum.endpoint | String | - | Ethereum RPC エンドポイント |
| websocket.uri.host | String | - | WebSocket ホスト |
| websocket.uri.port | String | - | WebSocket ポート |

## 3. 処理フロー

### メインフロー
```mermaid
flowchart TD
    A[開始] --> B[Web3j WebSocket接続作成]
    B --> C[newHeadsNotifications購読]
    C --> D[BlockingQueue作成]
    D --> E[非同期ブロック通知待機]
    
    E --> F{新規ブロック通知受信}
    F -->|受信| G[ブロック詳細取得]
    F -->|タイムアウト| E
    
    G --> H{ブロック遅延チェック}
    H -->|遅延あり| I[警告ログ出力]
    H -->|正常| J[トランザクション存在チェック]
    I --> J
    
    J -->|なし| K[ログ出力してスキップ]
    J -->|あり| L[イベントログ解析]
    K --> E
    
    L --> M[Transactionオブジェクト作成]
    M --> N[BlockingQueueにPUT]
    N --> E
    
    style F fill:#e1f5fe
    style N fill:#c8e6c9
```

### processNewTransactions処理フロー
```mermaid
flowchart TD
    A[processNewTransactions開始] --> B{ContextConfig.isServiceRunning?}
    B -->|false| Z[処理終了]
    B -->|true| C[BlockingQueue.poll 5秒待機]
    
    C --> D{Transaction取得}
    D -->|null| B
    D -->|取得| E{blockNumber == -1?}
    
    E -->|true| F[WebSocket切断エラー]
    E -->|false| G{blockNumber == 0?}
    
    G -->|true| H[ブロック高ゼロエラー]
    G -->|false| I[saveTransaction実行]
    
    I --> J{保存成功?}
    J -->|失敗| K[保存失敗エラー]
    J -->|成功| L[成功ログ出力]
    
    L --> B
    F --> M[例外スロー]
    H --> M
    K --> M
    
    style B fill:#fff3e0
    style I fill:#e8f5e8
    style M fill:#ffebee
```
