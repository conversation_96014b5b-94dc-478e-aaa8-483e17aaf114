# 基本設計書_BCMonitoring_新規ブロック処理機能

## 1. 概要

### 機能の目的と責務
- **目的**: Ethereumブロックチェーンから新しく生成されるブロックをリアルタイムで監視し、含まれるイベントログを検知・保存する
- **責務**: 
  - Web3j WebSocketを使用した新規ブロック通知の購読
  - 新規ブロック内のトランザクションとイベントログの解析
  - ABI定義に基づくイベントフィルタリング
  - DynamoDBへのイベントデータとブロック高の保存

### 他機能との関連性
- **依存機能**: 
  - ABI.jsonパース機能（イベント定義の取得）
  - ブロックのログをパースする機能（イベントログの解析）
- **後続機能**: 
  - リトライの仕組み（エラー時の再処理）
- **並行機能**: 
  - 過去ブロック処理機能（過去ブロック処理完了後に開始）

### 前提条件・制約事項
- **前提条件**:
  - Web3j WebSocket接続が確立されていること
  - ABI JSONがS3から取得済みであること
  - DynamoDBテーブルが作成済みであること
  - 過去ブロック処理が完了していること
- **制約事項**:
  - 1つのWebSocket接続のみサポート
  - ブロック遅延検知機能あり（設定可能）
  - メモリ使用量制限（BlockingQueueサイズ: Integer.MAX_VALUE）

## 2. 機能仕様

### 入力・出力仕様

#### 入力
| 項目 | 型 | 説明 | 取得元 |
|------|----|----|--------|
| 新規ブロック通知 | NewHeadsNotification | Web3jからの新規ブロック通知 | Ethereum WebSocket |
| ABI定義 | Map<String, ContractEvents> | コントラクトアドレス別のイベント定義 | AbiParser.contractEventStore |

#### 出力
| 項目 | 型 | 説明 | 保存先 |
|------|----|----|--------|
| イベントレコード | Event | 検知されたイベント情報 | DynamoDB Events Table |
| ブロック高レコード | BlockHeight | 処理済みブロック高 | DynamoDB BlockHeight Table |

### 処理条件・判定ロジック

#### ブロック処理条件
```java
// 1. ブロックにトランザクションが存在する
if (block.getTransactions() == null || block.getTransactions().isEmpty()) {
    // スキップ
}

// 2. ブロック遅延チェック
if (isDelayed(block, allowableDiff)) {
    // 警告ログ出力（処理は継続）
}

// 3. イベントログのABI定義存在チェック
if (abiParser.getABIEventByLog(ethLog) != null) {
    // イベント処理実行
}
```

#### エラー判定条件
```java
// 1. WebSocket切断検知
if (tx.blockHeight.blockNumber == -1) {
    throw new Exception("Websocket is disconnected");
}

// 2. 不正なブロック高
if (tx.blockHeight.blockNumber == 0) {
    throw new Exception("Block height Number is zero");
}

// 3. トランザクションハッシュ不正
if (e.transactionHash == null || e.transactionHash.isEmpty()) {
    return false; // 保存失敗
}
```

### 設定パラメータ

| パラメータ名 | 型 | デフォルト値 | 説明 |
|-------------|----|-----------|----|
| subscription.checkInterval | String | - | エラー時のsleep間隔（ミリ秒） |
| subscription.allowableBlockTimestampDiffSec | String | - | ブロック遅延許容時間（秒） |
| ethereum.endpoint | String | - | Ethereum RPC エンドポイント |
| websocket.uri.host | String | - | WebSocket ホスト |
| websocket.uri.port | String | - | WebSocket ポート |

## 3. 処理フロー

### メインフロー
```mermaid
flowchart TD
    A[開始] --> B[Web3j WebSocket接続作成]
    B --> C[newHeadsNotifications購読]
    C --> D[BlockingQueue作成]
    D --> E[非同期ブロック通知待機]
    
    E --> F{新規ブロック通知受信}
    F -->|受信| G[ブロック詳細取得]
    F -->|タイムアウト| E
    
    G --> H{ブロック遅延チェック}
    H -->|遅延あり| I[警告ログ出力]
    H -->|正常| J[トランザクション存在チェック]
    I --> J
    
    J -->|なし| K[ログ出力してスキップ]
    J -->|あり| L[イベントログ解析]
    K --> E
    
    L --> M[Transactionオブジェクト作成]
    M --> N[BlockingQueueにPUT]
    N --> E
    
    style F fill:#e1f5fe
    style N fill:#c8e6c9
```

### processNewTransactions処理フロー
```mermaid
flowchart TD
    A[processNewTransactions開始] --> B{ContextConfig.isServiceRunning?}
    B -->|false| Z[処理終了]
    B -->|true| C[BlockingQueue.poll 5秒待機]
    
    C --> D{Transaction取得}
    D -->|null| B
    D -->|取得| E{blockNumber == -1?}
    
    E -->|true| F[WebSocket切断エラー]
    E -->|false| G{blockNumber == 0?}
    
    G -->|true| H[ブロック高ゼロエラー]
    G -->|false| I[saveTransaction実行]
    
    I --> J{保存成功?}
    J -->|失敗| K[保存失敗エラー]
    J -->|成功| L[成功ログ出力]
    
    L --> B
    F --> M[例外スロー]
    H --> M
    K --> M
    
    style B fill:#fff3e0
    style I fill:#e8f5e8
    style M fill:#ffebee
```

### 異常系フロー
```mermaid
flowchart TD
    A[エラー発生] --> B{エラー種別判定}

    B -->|WebSocket接続エラー| C[unsubscribe実行]
    B -->|ブロック処理エラー| D[エラートランザクション生成]
    B -->|保存エラー| E[エラーログ出力]

    C --> F[Web3j shutdown]
    D --> G[blockNumber=-1でPUT]
    E --> H[例外スロー]

    F --> I[エラートランザクション生成]
    G --> J[処理継続]
    H --> K[上位層でキャッチ]

    I --> L[blockNumber=-1でPUT]
    L --> M[処理継続]

    style A fill:#ffebee
    style C fill:#fff3e0
    style H fill:#ffcdd2
```

## 4. クラス設計

### 関連クラス一覧

| クラス名 | パッケージ | 責務 |
|---------|-----------|------|
| MonitorEventService | application.event | 新規ブロック処理の制御・調整 |
| EthEventLogDao | infrastructure.ethereum | Web3j WebSocket購読・ブロック解析 |
| EventLogRepositoryImpl | infrastructure.ethereum | ブロックチェーンアクセスの抽象化 |
| AbiParser | infrastructure.s3 | ABI定義によるイベントフィルタリング |
| EventRepository | infrastructure.dynamodb | イベントデータの永続化 |
| BlockHeightRepository | infrastructure.dynamodb | ブロック高データの永続化 |
| Web3jConfig | config | Web3j接続管理 |
| ContextConfig | config | アプリケーション実行状態管理 |

### 主要メソッド仕様

#### MonitorEventService.processNewTransactions()
```java
/**
 * 新規ブロックのトランザクションを継続的に処理
 * @param transactionsQueue 新規ブロックのトランザクションキュー
 * @throws Exception 処理エラー時
 */
private void processNewTransactions(BlockingQueue<Transaction> transactionsQueue) throws Exception
```

**処理概要**:
1. ContextConfig.isServiceRunning()でループ制御
2. BlockingQueue.poll(5, TimeUnit.SECONDS)で非ブロッキング取得
3. トランザクション検証（blockNumber、transactionHash）
4. saveTransaction()でDynamoDB保存
5. エラー時は例外スローで上位層に委譲

#### EthEventLogDao.subscribeAll()
```java
/**
 * 新規ブロック通知を購読し、トランザクションキューを返却
 * @return 新規ブロックのトランザクションキュー
 */
public BlockingQueue<Transaction> subscribeAll()
```

**処理概要**:
1. LinkedBlockingQueue(Integer.MAX_VALUE)作成
2. Web3j.newHeadsNotifications()購読
3. 非同期コールバックでブロック処理
4. convBlock2EventEntities()でイベント解析
5. エラー時はblockNumber=-1のトランザクション生成

#### MonitorEventService.saveTransaction()
```java
/**
 * トランザクション内の全イベントとブロック高を保存
 * @param tx 保存対象トランザクション
 * @return 保存成功可否
 */
private boolean saveTransaction(Transaction tx)
```

**処理概要**:
1. Transaction内の全Eventを順次処理
2. StructuredLogContextでログコンテキスト設定
3. EventRepository.save()でイベント保存
4. BlockHeightRepository.save()でブロック高保存
5. 1つでも失敗した場合はfalse返却

### 依存関係
```mermaid
classDiagram
    MonitorEventService --> EventLogRepositoryImpl : uses
    MonitorEventService --> EventRepository : uses
    MonitorEventService --> BlockHeightRepository : uses
    MonitorEventService --> EthEventLogDao : uses

    EventLogRepositoryImpl --> EthEventLogDao : delegates
    EthEventLogDao --> Web3jConfig : uses
    EthEventLogDao --> AbiParser : uses

    EthEventLogDao --> Transaction : creates
    EthEventLogDao --> Event : creates
    EthEventLogDao --> BlockHeight : creates

    class MonitorEventService {
        -processNewTransactions()
        -saveTransaction()
    }

    class EthEventLogDao {
        +subscribeAll()
        -convBlock2EventEntities()
        -convertEthLogToEventEntity()
    }

    class EventLogRepositoryImpl {
        +subscribe()
    }
```

### データ構造

#### Transaction
```java
@Builder
public class Transaction {
    public final List<Event> events;           // イベントリスト
    public final BlockHeight blockHeight;      // ブロック高情報
}
```

#### Event
```java
@Builder
public class Event {
    public final String transactionHash;       // トランザクションハッシュ
    public final int logIndex;                 // ログインデックス
    public final String name;                  // イベント名
    public final String indexedValues;         // インデックス付きパラメータ（JSON）
    public final String nonIndexedValues;      // 非インデックスパラメータ（JSON）
    public final long blockTimestamp;          // ブロックタイムスタンプ
    public final String log;                   // 元ログ情報（JSON）
}
```

#### BlockHeight
```java
@Builder
public class BlockHeight {
    public final int id;                       // 固定値: 1
    public final long blockNumber;             // ブロック番号
}
```

### エラーハンドリング仕様

#### エラー分類と対応

| エラー分類 | 発生箇所 | 対応方法 | ログレベル |
|-----------|---------|---------|-----------|
| WebSocket接続エラー | EthEventLogDao.subscribeAll() | unsubscribe + shutdown + エラートランザクション生成 | ERROR |
| ブロック処理エラー | EthEventLogDao非同期コールバック | エラートランザクション生成（blockNumber=-1） | ERROR |
| 設定値エラー | allowableBlockTimestampDiffSec解析 | null返却 | ERROR |
| 保存エラー | saveTransaction() | false返却 + 例外スロー | ERROR |
| トランザクション検証エラー | processNewTransactions() | 例外スロー | ERROR |
| ブロック遅延 | isDelayed() | 警告ログのみ（処理継続） | WARN |

#### リトライ仕様
- **基本方針**: 新規ブロック処理機能自体はリトライしない
- **上位層委譲**: MonitoringRunnerConfigでの再起動に委譲
- **エラー通知**: blockNumber=-1のトランザクションで通知
- **リソース解放**: エラー時はunsubscribe()とshutdown()を実行

### 性能要件

#### 処理時間目標
- **ブロック通知受信〜キューPUT**: 100ms以内
- **キューPOLL〜DynamoDB保存完了**: 500ms以内
- **1ブロックあたりの処理時間**: 1秒以内

#### スループット要件
- **ブロック生成間隔**: 12秒（Ethereum標準）
- **同時処理イベント数**: 100イベント/ブロック
- **キューサイズ**: Integer.MAX_VALUE（メモリ許容範囲内）

#### リソース使用量
- **メモリ使用量**: BlockingQueueサイズに依存
- **WebSocket接続数**: 1接続（購読用）+ 1接続（RPC用）
- **DynamoDB書き込み**: イベント数 + 1（ブロック高）/ブロック
