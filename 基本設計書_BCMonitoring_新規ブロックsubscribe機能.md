# 基本設計書_BCMonitoring_新規ブロックsubscribe機能

## 1. 概要

### 機能の目的と責務
- **目的**: Ethereumブロックチェーンから新しく生成されるブロックをリアルタイムで購読し、ブロック情報をキューに蓄積する
- **責務**: 
  - Web3j WebSocketを使用した新規ブロック通知の購読
  - 新規ブロック通知の受信と詳細情報の取得
  - ブロック遅延の検知と警告
  - ブロック情報のTransactionオブジェクト化
  - BlockingQueueへの非同期蓄積

### 他機能との関連性
- **前提機能**: 
  - Web3j WebSocket接続管理（Web3jConfig）
  - ABI.jsonパース機能（イベントフィルタリング用）
- **後続機能**: 
  - 新規ブロック処理の機能（キューからの取得・処理）
  - ブロックのログをパースする機能（イベント解析）
- **独立性**: 
  - 購読処理は他機能から独立して動作
  - エラー時は自己完結的にエラー通知

### 前提条件・制約事項
- **前提条件**:
  - Web3j WebSocket接続が確立可能であること
  - Ethereumノードが新規ブロック通知をサポートしていること
  - 設定パラメータ（allowableBlockTimestampDiffSec）が正しく設定されていること
- **制約事項**:
  - 1つのWebSocket接続のみサポート（購読用 + RPC用）
  - BlockingQueueサイズ: Integer.MAX_VALUE（メモリ制限あり）
  - 非同期処理のため、処理順序は保証されない

## 2. 処理フロー

### メインフロー（購読開始）
```mermaid
@startuml

autonumber
entity MonitorEventService as MS
queue BlockingQueue as Q
entity EthEventLogDao as ELD
entity Web3j as W3J
database Ethereum as ETH

MS->>ELD: subscribeAll()
ELD->>W3J: Websocket接続作成
ELD->>W3J: newHeadsNotifications()購読
ELD->>Q: BlockingQueue作成

loop 新規ブロック監視
    ETH->>W3J: 新規ブロック生成
    W3J->>ELD: newHeadsNotification
    ELD->>W3J: ethGetBlockByNumber()
    W3J-->>ELD: ブロック詳細

    alt ブロック遅延チェック
        ELD->>ELD: isDelayed()
        Note over ELD: 遅延時は警告ログ
    end

    alt トランザクション存在
        ELD->>ELD: convBlock2EventEntities()
        ELD->>ELD: Transactionオブジェクト作成
        ELD->>Q: Transaction PUT
    else トランザクションなし
        Note over ELD: ログ出力してスキップ
    end
end
```

### 非同期ブロック処理フロー
```mermaid
sequenceDiagram
    participant ETH as Ethereum
    participant W3J as Web3j
    participant ELD as EthEventLogDao
    participant AP as AbiParser
    participant Q as BlockingQueue

    loop 新規ブロック監視
        ETH->>W3J: 新規ブロック生成
        W3J->>ELD: onNext(NewHeadsNotification)

        ELD->>W3J: ethGetBlockByNumber(blockNumber, true)
        W3J-->>ELD: EthBlock.Block

        ELD->>ELD: isDelayed()チェック
        alt ブロック遅延
            Note over ELD: 警告ログ出力（処理継続）
        end

        alt トランザクション存在
            loop 各トランザクション
                ELD->>W3J: ethGetTransactionReceipt()
                W3J-->>ELD: TransactionReceipt

                loop 各ログ
                    ELD->>AP: getABIEventByLog()
                    alt ABI定義存在
                        AP-->>ELD: Event定義
                        ELD->>ELD: convertEthLogToEventEntity()
                    else ABI定義なし
                        Note over ELD: ログスキップ
                    end
                end
            end

            ELD->>ELD: Transactionオブジェクト作成
            ELD->>Q: Transaction PUT
            Note over Q: 非同期でキューに蓄積
        else トランザクションなし
            Note over ELD: ログ出力してスキップ
        end
    end
```

### エラーハンドリングフロー
```mermaid
sequenceDiagram
    participant W3J as Web3j
    participant ELD as EthEventLogDao
    participant W3JC as Web3jConfig
    participant Q as BlockingQueue

    alt ブロック処理エラー（onNext内）
        W3J->>ELD: onNext(NewHeadsNotification)
        ELD->>ELD: ブロック処理でException発生
        ELD->>Q: エラートランザクション生成
        Note over Q: blockNumber=-1

    else WebSocket接続エラー（onError）
        W3J->>ELD: onError(Throwable)
        ELD->>ELD: unsubscribe()
        ELD->>W3JC: shutdownWeb3j()
        ELD->>Q: エラートランザクション生成
        Note over Q: blockNumber=-1

    else 購読完了（onComplete）
        W3J->>ELD: onComplete()
        Note over ELD: 購読完了ログ出力
    end
```

## 3. クラス設計

### 関連クラス一覧

| クラス名 | パッケージ | 責務 |
|---------|-----------|------|
| EthEventLogDao | infrastructure.ethereum | 新規ブロック購読の中核処理 |
| Web3jConfig | config | Web3j WebSocket接続管理 |
| EventLogRepositoryImpl | infrastructure.ethereum | 購読処理の抽象化レイヤー |
| AbiParser | infrastructure.s3 | イベントログのABI定義照合 |
| BcmonitoringConfigurationProperties | config | 設定パラメータ管理 |

### 主要メソッド仕様

#### EthEventLogDao.subscribeAll()
```java
/**
 * 新規ブロック通知を購読し、トランザクションキューを返却
 * @return 新規ブロックのトランザクションキュー（null: 設定エラー時）
 */
public BlockingQueue<Transaction> subscribeAll()
```

**処理概要**:
1. LinkedBlockingQueue(Integer.MAX_VALUE)作成
2. allowableBlockTimestampDiffSec設定値検証
3. Web3j接続取得（購読用・RPC用）
4. newHeadsNotifications()でFlowable取得
5. subscribe()で3つのコールバック登録
   - onNext: ブロック処理・キューPUT
   - onError: unsubscribe・shutdown・エラー通知
   - onComplete: 完了ログ出力

#### EthEventLogDao.unsubscribe()
```java
/**
 * WebSocket購読を停止
 */
public void unsubscribe()
```

**処理概要**:
1. subscription（Disposable）のnullチェック
2. subscription.dispose()でWebSocket切断

#### Web3jConfig.getWeb3j() / getWeb3jCaller()
```java
/**
 * Web3j接続インスタンスを取得（キャッシュ機能付き）
 * @return Web3jインスタンス
 * @throws Web3jConnectionException 接続失敗時
 */
public synchronized Web3j getWeb3j() throws Web3jConnectionException
```

**処理概要**:
1. キャッシュされたインスタンスの存在確認
2. 未作成時はcreateWebSocketWeb3j()で新規作成
3. WebSocketService接続・Web3j.build()実行

### 依存関係
```mermaid
classDiagram
    EthEventLogDao --> Web3jConfig : uses
    EthEventLogDao --> AbiParser : uses
    EthEventLogDao --> BcmonitoringConfigurationProperties : uses
    EthEventLogDao --> BlockingQueue : creates
    EthEventLogDao --> Transaction : creates

    Web3jConfig --> WebSocketService : creates
    Web3jConfig --> Web3j : creates

    EventLogRepositoryImpl --> EthEventLogDao : delegates

    class EthEventLogDao {
        -Disposable subscription
        +subscribeAll() BlockingQueue~Transaction~
        +unsubscribe() void
        -isDelayed() boolean
        -convBlock2EventEntities() List~Event~
    }

    class Web3jConfig {
        -Web3j web3j
        -Web3j web3jCaller
        +getWeb3j() Web3j
        +getWeb3jCaller() Web3j
        +shutdownWeb3j() void
    }

    class EventLogRepositoryImpl {
        +subscribe() BlockingQueue~Transaction~
    }
```

### データ構造

#### NewHeadsNotification（Web3j提供）
```java
// Web3jライブラリが提供する新規ブロック通知
public class NewHeadsNotification {
    private NewHeadsNotificationParameter params;

    public class NewHeadsNotificationParameter {
        private EthBlock.Block result; // ブロック基本情報
    }
}
```

#### BlockingQueue<Transaction>
```java
// 新規ブロック情報を蓄積するキュー
BlockingQueue<Transaction> transactions = new LinkedBlockingQueue<>(Integer.MAX_VALUE);

// 正常なTransaction
Transaction.builder()
    .events(List<Event>)           // 解析されたイベントリスト
    .blockHeight(BlockHeight)      // ブロック高情報
    .build()

// エラー通知Transaction
Transaction.builder()
    .blockHeight(BlockHeight.builder().blockNumber(-1).build())
    .build()
```

#### Disposable（RxJava）
```java
// WebSocket購読の制御オブジェクト
private Disposable subscription;

// 購読開始
this.subscription = web3j.newHeadsNotifications().subscribe(...);

// 購読停止
if (subscription != null) {
    subscription.dispose();
}
```

### エラーハンドリング仕様

#### エラー分類と対応

| エラー分類 | 発生箇所 | 対応方法 | ログレベル | 戻り値/通知 |
|-----------|---------|---------|-----------|------------|
| 設定値エラー | allowableBlockTimestampDiffSec解析 | null返却 | ERROR | null |
| WebSocket接続エラー | Web3jConfig.createWebSocketWeb3j() | 例外スロー | ERROR | Web3jConnectionException |
| 購読作成エラー | newHeadsNotifications().subscribe() | 例外スロー | ERROR | RuntimeException |
| ブロック処理エラー | 非同期onNextコールバック | エラートランザクション生成 | ERROR | blockNumber=-1 |
| WebSocket切断エラー | 非同期onErrorコールバック | unsubscribe + shutdown + エラー通知 | ERROR | blockNumber=-1 |
| ブロック遅延 | isDelayed()チェック | 警告ログのみ（処理継続） | WARN | 処理継続 |

#### Flowable購読の3つのコールバック
```java
web3j.newHeadsNotifications().subscribe(
    // onNext: 新規ブロック通知受信時
    newHeadsNotification -> {
        try {
            // ブロック詳細取得・解析・キューPUT
        } catch (Exception throwable) {
            // エラートランザクション生成（blockNumber=-1）
        }
    },

    // onError: WebSocket接続エラー時
    error -> {
        logger.error("Subscription error", error);
        unsubscribe();
        web3jConfig.shutdownWeb3j();
        // エラートランザクション生成（blockNumber=-1）
    },

    // onComplete: 購読完了時
    () -> logger.info("Subscription completed")
);
```

### 性能要件

#### 処理時間目標
- **購読開始時間**: 1秒以内
- **ブロック通知受信〜キューPUT**: 100ms以内
- **WebSocket接続確立**: 3秒以内

#### スループット要件
- **ブロック生成間隔**: 12秒（Ethereum標準）
- **同時処理ブロック数**: 1ブロック（順次処理）
- **キューサイズ**: Integer.MAX_VALUE（メモリ許容範囲内）

#### リソース使用量
- **WebSocket接続数**: 2接続（購読用 + RPC用）
- **メモリ使用量**: BlockingQueueサイズ × Transactionサイズ
- **CPU使用量**: 非同期処理のため最小限
